/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

'use strict';

import AnimatedProps from './nodes/AnimatedProps';
import { AnimatedEvent } from './AnimatedEvent';
import useRefEffect from '../Utilities/useRefEffect';
import NativeAnimatedHelper from './NativeAnimatedHelper';
import { useCallback, useEffect, useMemo, useReducer, useRef } from 'react';
import useLayoutEffect from '../../../modules/useLayoutEffect';
type ReducedProps<TProps> = {
  ...TProps,
  collapsable: boolean,
  ...
};
type CallbackRef<T> = (T) => mixed;
declare export default function useAnimatedProps<TProps: {...}, TInstance>(props: TProps): [ReducedProps<TProps>, CallbackRef<TInstance | null>];
declare function reduceAnimatedProps<TProps>(node: AnimatedProps): ReducedProps<TProps>;
/**
 * Manages the lifecycle of the supplied `AnimatedProps` by invoking `__attach`
 * and `__detach`. However, this is more complicated because `AnimatedProps`
 * uses reference counting to determine when to recursively detach its children
 * nodes. So in order to optimize this, we avoid detaching until the next attach
 * unless we are unmounting.
 */
declare function useAnimatedPropsLifecycle(node: AnimatedProps): void;
declare function getEventTarget<TInstance>(instance: TInstance): TInstance; // $FlowFixMe[unclear-type] - Legacy instance assumptions.
declare function isFabricInstance(instance: any): boolean; // $FlowFixMe[unclear-type] - Legacy instance assumptions.
declare function hasFabricHandle(instance: any): boolean;
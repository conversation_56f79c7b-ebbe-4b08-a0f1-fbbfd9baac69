/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @providesModule removeFromArray
 * @typechecks
 * @flow
 */

/**
 * Removes an element from an array.
 */
function removeFromArray<T>(array: Array<T>, element: T): void {
  var index = array.indexOf(element);

  if (index !== -1) {
    array.splice(index, 1);
  }
}

module.exports = removeFromArray;
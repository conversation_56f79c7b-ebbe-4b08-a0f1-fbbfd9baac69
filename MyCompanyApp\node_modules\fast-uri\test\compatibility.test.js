'use strict'

const test = require('tape')
const fastifyURI = require('../')
const urijs = require('uri-js')

test('compatibility Parse', (t) => {
  const toParse = [
    '//www.g.com/error\n/bleh/bleh',
    'https://fastify.org',
    '/definitions/Record%3Cstring%2CPerson%3E',
    '//***********',
    // '//10.10.000.10', <-- not a valid URI per URI spec: https://datatracker.ietf.org/doc/html/rfc5954#section-4.1
    '//[2001:db8::7%en0]',
    '//[2001:dbZ::1]:80',
    '//[2001:db8::1]:80',
    '//[2001:db8::001]:80',
    'uri://user:<EMAIL>:123/one/two.three?q1=a1&q2=a2#body',
    'http://user:<EMAIL>:123/one/space in.url?q1=a1&q2=a2#body',
    'http://User:<EMAIL>:123/one/space in.url?q1=a1&q2=a2#body',
    'http://<EMAIL>:123/one/space',
    '//[::ffff:*************]',
    'uri://***********.example.com/en/process',
    '//[2606:2800:220:1:248:1893:25c8:1946]/test',
    'ws://example.com/chat',
    'ws://example.com/foo?bar=baz',
    'wss://example.com/?bar=baz',
    'wss://example.com/chat',
    'wss://example.com/foo?bar=baz',
    'wss://example.com/?bar=baz',
    'urn:uuid:f81d4fae-7dec-11d0-a765-00a0c91e6bf6',
    'urn:uuid:notauuid-7dec-11d0-a765-00a0c91e6bf6',
    'urn:example:%D0%B0123,z456',
    '//[2606:2800:220:1:248:1893:25c8:1946:43209]',
    'http://foo.bar',
    'http://',
    '#/$defs/stringMap',
    '#/$defs/string%20Map',
    '#/$defs/string Map',
    '//?json=%7B%22foo%22%3A%22bar%22%7D'
    //  'mailto:<EMAIL>'-203845,
    //  'mailto:<EMAIL>?subject=current-issue',
    //  'mailto:<EMAIL>?body=send%20current-issue',
    //  'mailto:<EMAIL>?body=send%20current-issue%0D%0Asend%20index',
    //  'mailto:<EMAIL>?In-Reply-To=%<EMAIL>%3E',
    //  'mailto:<EMAIL>?body=subscribe%20bamboo-l',
    //  'mailto:<EMAIL>?cc=<EMAIL>&body=hello',
    //  'mailto:<EMAIL>',
    //  'mailto:<EMAIL>?blat=foop',
    //  'mailto:<EMAIL>',
    //  'mailto:%<EMAIL>',
    //  'mailto:%<EMAIL>',
    //  'mailto:%22%5C%5C%5C%22it\'<EMAIL>',
    //  'mailto:<EMAIL>?subject=caf%C3%A9',
    //  'mailto:<EMAIL>?subject=%3D%3Futf-8%3FQ%3Fcaf%3DC3%3DA9%3F%3D',
    //  'mailto:<EMAIL>?subject=%3D%3Fiso-8859-1%3FQ%3Fcaf%3DE9%3F%3D',
    //  'mailto:<EMAIL>?subject=caf%C3%A9&body=caf%C3%A9',
    //  'mailto:user@%E7%B4%8D%E8%B1%86.example.org?subject=Test&body=NATTO'
  ]
  toParse.forEach((x) => {
    t.same(fastifyURI.parse(x), urijs.parse(x), 'Compatibility parse: ' + x)
  })
  t.end()
})

test('compatibility serialize', (t) => {
  const toSerialize = [
    { host: '***********.example.com' },
    { host: '2001:db8::7' },
    { host: '::ffff:*************' },
    { host: '2606:2800:220:1:248:1893:25c8:1946' },
    { host: '***********.example.com' },
    { host: '***********' },
    { path: '?query' },
    { path: 'foo:bar' },
    { path: '//path' },
    {
      scheme: 'uri',
      host: 'example.com',
      port: '9000'
    },
    {
      scheme: 'uri',
      userinfo: 'foo:bar',
      host: 'example.com',
      port: 1,
      path: 'path',
      query: 'query',
      fragment: 'fragment'
    },
    {
      scheme: '',
      userinfo: '',
      host: '',
      port: 0,
      path: '',
      query: '',
      fragment: ''
    },
    {
      scheme: undefined,
      userinfo: undefined,
      host: undefined,
      port: undefined,
      path: undefined,
      query: undefined,
      fragment: undefined
    },
    { host: 'fe80::a%en1' },
    { host: 'fe80::a%25en1' },
    {
      scheme: 'ws',
      host: 'example.com',
      resourceName: '/foo?bar',
      secure: true
    },
    {
      scheme: 'scheme',
      path: 'with:colon'
    }
  ]
  toSerialize.forEach((x) => {
    const r = JSON.stringify(x)
    t.same(
      fastifyURI.serialize(x),
      urijs.serialize(x),
      'Compatibility serialize: ' + JSON.stringify(r)
    )
  })
  t.end()
})

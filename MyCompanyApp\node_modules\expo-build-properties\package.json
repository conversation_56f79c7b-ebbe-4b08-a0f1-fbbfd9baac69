{"name": "expo-build-properties", "version": "0.14.6", "description": "Config plugin to customize native build properties on prebuild", "main": "build/withBuildProperties.js", "types": "build/withBuildProperties.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "build", "build-properties"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-build-properties"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/build-properties", "dependencies": {"ajv": "^8.11.0", "semver": "^7.6.0"}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}
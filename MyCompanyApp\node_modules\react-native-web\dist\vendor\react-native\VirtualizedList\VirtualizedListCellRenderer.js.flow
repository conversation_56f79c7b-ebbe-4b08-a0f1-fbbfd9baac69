/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

import type { LayoutEvent } from '../../../types';
import type { FocusEvent } from '../Types/CoreEventTypes';
import type { CellRendererProps, RenderItemType } from './VirtualizedListProps';
import View, { type ViewProps } from '../../../exports/View';
import StyleSheet from '../../../exports/StyleSheet';
import { VirtualizedListCellContextProvider } from './VirtualizedListContext.js';
import invariant from 'fbjs/lib/invariant';
import * as React from 'react';
type ViewStyleProp = $PropertyType<ViewProps, 'style'>;
export type Props<ItemT> = {
  CellRendererComponent?: ?React.ComponentType<CellRendererProps<ItemT>>,
  ItemSeparatorComponent: ?React.ComponentType<any | {
    highlighted: boolean,
    leadingItem: ?ItemT,
  }>,
  ListItemComponent?: ?(React.ComponentType<any> | React.Element<any>),
  cellKey: string,
  horizontal: ?boolean,
  index: number,
  inversionStyle: ViewStyleProp,
  item: ItemT,
  onCellLayout?: (event: LayoutEvent, cellKey: string, index: number) => void,
  onCellFocusCapture?: (event: FocusEvent) => void,
  onUnmount: (cellKey: string) => void,
  onUpdateSeparators: (cellKeys: Array<?string>, props: $Shape<SeparatorProps<ItemT>>) => void,
  prevCellKey: ?string,
  renderItem?: ?RenderItemType<ItemT>,
  ...
};
type SeparatorProps<ItemT> = $ReadOnly<{|
  highlighted: boolean,
  leadingItem: ?ItemT,
|}>;
type State<ItemT> = {
  separatorProps: SeparatorProps<ItemT>,
  ...
};
declare export default class CellRenderer<ItemT> extends React.Component<Props<ItemT>, State<ItemT>> {
  state: State<ItemT>,
  static getDerivedStateFromProps(props: Props<ItemT>, prevState: State<ItemT>): ?State<ItemT>,
  _separators: any,
  updateSeparatorProps(newProps: SeparatorProps<ItemT>): any,
  componentWillUnmount(): any,
  _onLayout: any,
  _renderElement(renderItem: ?RenderItemType<ItemT>, ListItemComponent: any, item: ItemT, index: number): React.Node,
  render(): React.Node,
}
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row'
  },
  rowReverse: {
    flexDirection: 'row-reverse'
  },
  columnReverse: {
    flexDirection: 'column-reverse'
  }
});
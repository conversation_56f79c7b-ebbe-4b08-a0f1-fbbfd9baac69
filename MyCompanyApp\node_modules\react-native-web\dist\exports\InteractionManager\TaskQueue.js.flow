/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import invariant from 'fbjs/lib/invariant';
type SimpleTask = {|
  name: string,
  run: () => void,
|};
type PromiseTask = {|
  name: string,
  gen: () => Promise<void>,
|};
export type Task = SimpleTask | PromiseTask | (() => void);
declare class TaskQueue {
  constructor(arg0: {
    onMoreTasks: () => void,
    ...
  }): any,
  enqueue(task: Task): void,
  enqueueTasks(tasks: Array<Task>): void,
  cancelTasks(tasksToCancel: Array<Task>): void,
  hasTasksToProcess(): boolean,
  processNext(): void,
  _queueStack: Array<{
    tasks: Array<Task>,
    popable: boolean,
    ...
  }>,
  _onMoreTasks: () => void,
  _getCurrentQueue(): Array<Task>,
  _genPromise(task: PromiseTask): any,
}
export default TaskQueue;
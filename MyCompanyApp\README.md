# تطبيق الشركة - MyCompanyApp 🏢

## 📱 تطبيق ويب لعرض موقع الشركة

هذا التطبيق يعرض موقع الشركة https://quickly24erp.com/login داخل تطبيق أندرويد أصلي باستخدام WebView.

### ✅ المميزات المتوفرة:

#### 1. **عرض الموقع:**
- عرض موقع الشركة بشكل كامل
- دعم JavaScript والتفاعل الكامل
- تجربة مستخدم أصلية

#### 2. **مميزات التطبيق:**
- زر الرجوع يعمل داخل الموقع
- رسائل خطأ باللغة العربية
- إعادة تحميل عند فشل الاتصال
- دعم كامل للهواتف والأجهزة اللوحية

#### 3. **التقنيات المستخدمة:**
- React Native
- Expo
- react-native-webview
- expo-build-properties

## 🎯 كيفية تشغيل التطبيق:

### على الويب:
```bash
npx expo start --web
```

### على الأندرويد:
```bash
npx expo start --android
```

### على الهاتف الحقيقي:
1. ثبت تطبيق Expo Go من Google Play
2. امسح QR Code الظاهر في Terminal
3. سيفتح التطبيق مباشرة

## 🛠️ إعدادات التطبيق:

### معلومات التطبيق:
- **الاسم:** تطبيق الشركة
- **Package:** com.company.webapp
- **الإصدار:** 1.0.0

### الأذونات المطلوبة:
- INTERNET (للوصول للموقع)
- ACCESS_NETWORK_STATE (لفحص الاتصال)

## 📱 اختبار التطبيق:

1. **على الويب:** http://localhost:8081
2. **على الهاتف:** امسح QR Code
3. **على المحاكي:** اضغط 'a' في terminal

## 🎨 تخصيص التطبيق:

### تغيير الموقع:
عدل الرابط في `App.js`:
```javascript
source={{ uri: 'https://your-website.com' }}
```

### تغيير الأيقونة:
استبدل الملفات في مجلد `assets/`:
- `icon.png` (1024x1024)
- `adaptive-icon.png` (1024x1024)
- `splash-icon.png` (1024x1024)

## 🚀 بناء التطبيق للإنتاج:

```bash
# بناء APK للأندرويد
npx eas build --platform android

# بناء للـ App Store (iOS)
npx eas build --platform ios
```

---

**التطبيق جاهز للاستخدام! 🎉**

**الموقع المعروض:** https://quickly24erp.com/login

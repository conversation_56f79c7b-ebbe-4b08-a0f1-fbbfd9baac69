/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @providesModule keyMirror
 * @typechecks static-only
 * @flow
 */
'use strict';

var invariant = require("./invariant");
/**
 * Constructs an enumeration with keys equal to their value.
 *
 * For example:
 *
 *   var COLORS = keyMirror({blue: null, red: null});
 *   var myColor = COLORS.blue;
 *   var isColorValid = !!COLORS[myColor];
 *
 * The last line could not be performed if the values of the generated enum were
 * not equal to their keys.
 *
 *   Input:  {key1: val1, key2: val2}
 *   Output: {key1: key1, key2: key2}
 *
 * @param {object} obj
 * @return {object}
 */


var keyMirror = function <T: {}>(obj: T): $ObjMapi<T, <K>(K) => K> {
  var ret = {};
  var key;
  invariant(obj instanceof Object && !Array.isArray(obj), 'keyMirror(...): Argument must be an object.');

  for (key in obj) {
    if (!obj.hasOwnProperty(key)) {
      continue;
    }

    ret[key] = key;
  }

  return ret;
};

module.exports = keyMirror;
# تطبيق الشركة - TestApp

## 🚀 بيئة التطوير جاهزة!

### ✅ ما تم إعداده:

#### 1. **اللغات والأدوات:**
- Node.js v22.12.0
- Python 3.13.0
- Java JDK 17
- React Native CLI
- Expo CLI v0.24.14
- Android Studio 2024.3.2.15

#### 2. **إضافات VS Code:**
- React Native Tools
- Android iOS Emulator
- ES7+ React/Redux/React-Native snippets

#### 3. **متغيرات البيئة:**
- ANDROID_HOME: `C:\Users\<USER>\AppData\Local\Android\Sdk`
- PATH: يتضمن أدوات Android SDK

#### 4. **محاكي الأندرويد:**
- محاكي جاهز: `Medium_Phone_API_36.0`

## 🎯 كيفية تشغيل التطبيق:

### على الويب:
```bash
npx expo start --web
```

### على الأندرويد:
```bash
npx expo start --android
```

### على iOS (يتطلب macOS):
```bash
npx expo start --ios
```

## 🛠️ أوامر مفيدة:

### تشغيل المحاكي:
```bash
# قائمة المحاكيات المتاحة
emulator -list-avds

# تشغيل محاكي محدد
emulator -avd Medium_Phone_API_36.0
```

### تطوير التطبيق:
```bash
# تشغيل مع إعادة التحميل التلقائي
npx expo start

# تثبيت مكتبة جديدة
npx expo install [package-name]

# بناء التطبيق للإنتاج
npx expo build:android
```

## 📱 اختبار التطبيق:

1. **على الويب:** افتح http://localhost:8081
2. **على الهاتف:** امسح QR Code باستخدام تطبيق Expo Go
3. **على المحاكي:** اضغط 'a' في terminal لفتح على الأندرويد

## 🎨 بدء التطوير:

1. افتح `App.js` لتعديل التطبيق
2. احفظ الملف لرؤية التغييرات فوراً
3. استخدم VS Code للاستفادة من الإضافات المثبتة

---

**البيئة جاهزة 100% للتطوير! 🎉**

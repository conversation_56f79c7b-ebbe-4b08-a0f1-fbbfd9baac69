/* eslint-disable */
/**
 * JS Implementation of MurmurHash2
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @see http://github.com/garycourt/murmurhash-js
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON> Appleby</a>
 * @see http://sites.google.com/site/murmurhash/
 *
 * @param {string} str ASCII only
 * @param {number} seed Positive integer only
 * @return {number} 32-bit positive integer hash
 *
 * @flow
 */
declare function murmurhash2_32_gc(str: any, seed: any): any;
declare var hash: (str: string) => string;
export default hash;
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, Alert, BackHandler } from 'react-native';
import { WebView } from 'react-native-webview';
import { useEffect, useRef } from 'react';

export default function App() {
  const webViewRef = useRef(null);

  // التعامل مع زر الرجوع في الأندرويد
  useEffect(() => {
    const backAction = () => {
      if (webViewRef.current) {
        webViewRef.current.goBack();
        return true; // منع الخروج من التطبيق
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );

    return () => backHandler.remove();
  }, []);

  // التعامل مع الأخطاء
  const handleError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    Alert.alert(
      'خطأ في الاتصال',
      'تعذر تحميل الموقع. تأكد من اتصالك بالإنترنت.',
      [{ text: 'إعادة المحاولة', onPress: () => webViewRef.current?.reload() }]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <WebView
        ref={webViewRef}
        source={{ uri: 'https://quickly24erp.com/login' }}
        style={styles.webview}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        allowsBackForwardNavigationGestures={true}
        onError={handleError}
        onHttpError={handleError}
        userAgent="Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  webview: {
    flex: 1,
  },
});

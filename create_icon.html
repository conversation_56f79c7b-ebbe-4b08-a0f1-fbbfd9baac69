<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة التطبيق</title>
</head>
<body>
    <canvas id="canvas" width="1024" height="1024"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // خلفية زرقاء
        ctx.fillStyle = '#2196F3';
        ctx.fillRect(0, 0, 1024, 1024);
        
        // دائرة بيضاء في المنتصف
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(512, 512, 300, 0, 2 * Math.PI);
        ctx.fill();
        
        // نص الشركة
        ctx.fillStyle = '#2196F3';
        ctx.font = 'bold 120px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('شركة', 512, 480);
        ctx.fillText('ERP', 512, 580);
        
        // تحويل إلى صورة وتحميلها
        const link = document.createElement('a');
        link.download = 'icon.png';
        link.href = canvas.toDataURL();
        link.click();
    </script>
</body>
</html>
